FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Install PostgreSQL client for pg_isready command
RUN apt-get update && apt-get install -y postgresql-client && rm -rf /var/lib/apt/lists/*

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Install Entity Framework tools version 8
RUN dotnet tool install --global dotnet-ef --version 8.0.11
ENV PATH="$PATH:/root/.dotnet/tools"

COPY ["Cherish.Api/Cherish.Api.csproj", "Cherish.Api/"]
COPY ["Cherish.Core/Cherish.Core.csproj", "Cherish.Core/"]
COPY ["Cherish.Persistence/Cherish.Persistence.csproj", "Cherish.Persistence/"]
COPY ["Cherish.Data/Cherish.Data.csproj", "Cherish.Data/"]
RUN dotnet restore "Cherish.Api/Cherish.Api.csproj"
COPY . .
WORKDIR "/src/Cherish.Api"
RUN dotnet build "Cherish.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Cherish.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app

# Install .NET SDK in the final image for EF tools (needed for migrations)
RUN apt-get update && apt-get install -y wget && \
    wget https://packages.microsoft.com/config/debian/12/packages-microsoft-prod.deb -O packages-microsoft-prod.deb && \
    dpkg -i packages-microsoft-prod.deb && \
    apt-get update && \
    apt-get install -y dotnet-sdk-8.0 && \
    rm -rf /var/lib/apt/lists/* && \
    rm packages-microsoft-prod.deb

# Install Entity Framework tools version 8 in final image
RUN dotnet tool install --global dotnet-ef --version 8.0.11
ENV PATH="$PATH:/root/.dotnet/tools"

# Copy published application
COPY --from=publish /app/publish .

# Copy necessary project files and source code for EF migrations with build artifacts
COPY --from=build /src /src

# Ensure the build artifacts are available for EF migrations
WORKDIR /src/Cherish.Api
RUN dotnet build "Cherish.Api.csproj" -c Release --no-restore

# Change back to app directory for application startup
WORKDIR /app

# Copy entrypoint script
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Set environment variables for database connection parsing
ENV DB_HOST=postgres
ENV DB_PORT=5432
ENV DB_NAME=cherish
ENV DB_USER=cherish_user

ENTRYPOINT ["/entrypoint.sh"]
