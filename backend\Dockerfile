# Use SDK image for everything to avoid NuGet cache issues
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Install PostgreSQL client for pg_isready command
RUN apt-get update && apt-get install -y postgresql-client && rm -rf /var/lib/apt/lists/*

# Install Entity Framework tools version 8
RUN dotnet tool install --global dotnet-ef --version 8.0.11
ENV PATH="$PATH:/root/.dotnet/tools"

# Copy project files and restore dependencies
WORKDIR /src
COPY ["Cherish.Api/Cherish.Api.csproj", "Cherish.Api/"]
COPY ["Cherish.Core/Cherish.Core.csproj", "Cherish.Core/"]
COPY ["Cherish.Persistence/Cherish.Persistence.csproj", "Cherish.Persistence/"]
COPY ["Cherish.Data/Cherish.Data.csproj", "Cherish.Data/"]

# Restore packages with clean cache
RUN dotnet restore "Cherish.Api/Cherish.Api.csproj" --no-cache

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/Cherish.Api"
RUN dotnet build "Cherish.Api.csproj" -c Release -o /app/build

# Publish the application
RUN dotnet publish "Cherish.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Set working directory for runtime
WORKDIR /app

# Copy published files
COPY --from=0 /app/publish .

# Keep source files available for EF migrations
COPY --from=0 /src /src

# Copy entrypoint script
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Set environment variables for database connection parsing
ENV DB_HOST=postgres
ENV DB_PORT=5432
ENV DB_NAME=cherish
ENV DB_USER=cherish_user

ENTRYPOINT ["/entrypoint.sh"]
