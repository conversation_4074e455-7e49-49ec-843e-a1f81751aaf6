#!/bin/bash
set -e

echo "🚀 Starting Cherish API container..."

# Function to wait for database to be ready
wait_for_db() {
    echo "⏳ Waiting for database to be ready..."

    # Extract database connection details from connection string
    DB_HOST=${DB_HOST:-postgres}
    DB_PORT=${DB_PORT:-5432}
    DB_NAME=${DB_NAME:-cherish}
    DB_USER=${DB_USER:-cherish_user}

    # Wait for PostgreSQL to be ready
    until pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -q; do
        echo "⏳ Database is not ready yet. Waiting 2 seconds..."
        sleep 2
    done

    echo "✅ Database is ready!"
}

# Function to run database migrations
run_migrations() {
    echo "🔄 Running database migrations..."

    # Change to the Data project directory where migrations are located
    cd /src/Cherish.Data

    # Run Entity Framework migrations with explicit startup project path
    # Remove --no-build flag since we need to ensure build artifacts are available
    if dotnet ef database update --verbose --startup-project /src/Cherish.Api --configuration Release; then
        echo "✅ Database migrations completed successfully!"
    else
        echo "⚠️  Migration failed or no migrations to run"
        echo "🔍 Attempting to run migrations from startup project directory..."

        # Fallback: try running from the startup project directory
        cd /src/Cherish.Api
        if dotnet ef database update --verbose --project ../Cherish.Data --configuration Release; then
            echo "✅ Database migrations completed successfully from startup project!"
        else
            echo "⚠️  Migration failed - continuing with application startup"
        fi
    fi

    # Change back to app directory for application startup
    cd /app
}

# Function to start the application
start_application() {
    echo "🎯 Starting Cherish API application..."
    exec dotnet Cherish.Api.dll
}

# Main execution flow
main() {
    echo "🏁 Cherish API Container Startup"
    echo "================================"

    # Wait for database to be available
    wait_for_db

    # Run database migrations
    run_migrations

    # Start the main application
    start_application
}

# Execute main function
main "$@"
